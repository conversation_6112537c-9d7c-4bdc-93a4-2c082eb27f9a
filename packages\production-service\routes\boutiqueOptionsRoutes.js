const express = require('express');
const router = express.Router();
const BoutiqueOptionsController = require('../controllers/boutiqueOptionsController');
const { validateInput } = require('../middleware/validateInput');
const auth = require('../middleware/auth');

// Validation schemas
const createOptionSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 255,
    trim: true
  },
  price: {
    required: true,
    type: 'number',
    min: 0,
    max: 999999.99
  }
};

const updateOptionSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 255,
    trim: true
  },
  price: {
    required: true,
    type: 'number',
    min: 0,
    max: 999999.99
  }
};

const querySchema = {
  page: {
    required: false,
    type: 'number',
    min: 1,
    default: 1
  },
  limit: {
    required: false,
    type: 'number',
    min: 1,
    max: 100,
    default: 10
  },
  search: {
    required: false,
    type: 'string',
    maxLength: 255
  },
  sortBy: {
    required: false,
    type: 'string',
    enum: ['name', 'price', 'created_at', 'updated_at'],
    default: 'name'
  },
  sortOrder: {
    required: false,
    type: 'string',
    enum: ['ASC', 'DESC'],
    default: 'ASC'
  }
};

// Routes

/**
 * @route GET /api/boutique-options
 * @desc Get all boutique options with pagination and search
 * @access Private
 */
router.get('/', 
  auth, 
  validateInput(querySchema, 'query'),
  BoutiqueOptionsController.getAllOptions
);

/**
 * @route GET /api/boutique-options/search
 * @desc Search boutique options by name
 * @access Private
 */
router.get('/search', 
  auth,
  BoutiqueOptionsController.searchOptions
);

/**
 * @route GET /api/boutique-options/:id
 * @desc Get a single boutique option by ID
 * @access Private
 */
router.get('/:id', 
  auth,
  BoutiqueOptionsController.getOptionById
);

/**
 * @route POST /api/boutique-options
 * @desc Create a new boutique option
 * @access Private
 */
router.post('/', 
  auth,
  validateInput(createOptionSchema, 'body'),
  BoutiqueOptionsController.createOption
);

/**
 * @route PUT /api/boutique-options/:id
 * @desc Update a boutique option
 * @access Private
 */
router.put('/:id', 
  auth,
  validateInput(updateOptionSchema, 'body'),
  BoutiqueOptionsController.updateOption
);

/**
 * @route DELETE /api/boutique-options/:id
 * @desc Delete a boutique option
 * @access Private
 */
router.delete('/:id', 
  auth,
  BoutiqueOptionsController.deleteOption
);

module.exports = router;
