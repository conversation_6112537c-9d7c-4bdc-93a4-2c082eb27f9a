<?php

namespace App\Http\Controllers;

use App\Models\OrderBoutique;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BoutiqueController extends Controller
{
    public function orderIndex()
    {
        $orders = OrderBoutique::with('customer')->get();
        return response()->json($orders);
    }

    public function orderShow($id)
    {
        $order = OrderBoutique::with('customer')->findOrFail($id);
        return response()->json($order);
    }

    public function orderStore(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'delivery_date' => 'required|date',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx'
        ]);

        $data = $request->only(['customer_id', 'delivery_date', 'status']);
        $data['created_by'] = auth()->id();

        if ($request->hasFile('attachment')) {
            $data['attachment'] = $request->file('attachment')->store('order_attachments');
        }

        $order = OrderBoutique::create($data);

        return response()->json($order, 201);
    }

    public function orderUpdate(Request $request, $id)
    {
        $order = OrderBoutique::findOrFail($id);

        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'delivery_date' => 'required|date',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx'
        ]);

        $data = $request->only(['customer_id', 'delivery_date', 'status']);
        $data['updated_by'] = auth()->id();

        if ($request->hasFile('attachment')) {
            // Delete old file if exists
            if ($order->attachment) {
                Storage::delete($order->attachment);
            }
            $data['attachment'] = $request->file('attachment')->store('order_attachments');
        }

        $order->update($data);

        return response()->json($order);
    }

    public function orderDestroy($id)
    {
        $order = OrderBoutique::findOrFail($id);
        if ($order->attachment) {
            Storage::delete($order->attachment);
        }
        $order->delete();
        return response()->json(['message' => 'Order deleted']);
    }
}