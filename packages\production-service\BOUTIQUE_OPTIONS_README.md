# Boutique Options Management System

This system provides a complete CRUD interface for managing boutique options with search, pagination, and sorting functionality.

## Database Table Structure

The system uses the `boutique_options` table with the following structure:

```sql
CREATE TABLE boutique_options (
    id INT(10) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    UNIQUE KEY unique_name (name)
);
```

## API Endpoints

All endpoints are prefixed with `/api/production/boutique-options`

### GET /
- **Description**: Get all boutique options with pagination and search
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `limit` (optional): Items per page (default: 10, max: 100)
  - `search` (optional): Search by name
  - `sortBy` (optional): Sort field (name, price, created_at, updated_at)
  - `sortOrder` (optional): Sort order (ASC, DESC)
- **Response**: Paginated list of options

### GET /:id
- **Description**: Get a single boutique option by ID
- **Response**: Single option object

### POST /
- **Description**: Create a new boutique option
- **Body**: `{ "name": "Option Name", "price": 99.99 }`
- **Validation**: Name required, unique; Price >= 0

### PUT /:id
- **Description**: Update an existing boutique option
- **Body**: `{ "name": "Updated Name", "price": 149.99 }`
- **Validation**: Same as POST

### DELETE /:id
- **Description**: Delete a boutique option
- **Response**: Success/error message

### GET /search
- **Description**: Search options by name
- **Query Parameters**: `q` (search term), `limit` (optional)

## Files Created

### Backend (Node.js/Express)
1. **Model**: `packages/production-service/models/BoutiqueOption.js`
   - Sequelize model with validation
   - Custom methods for search and formatting

2. **Controller**: `packages/production-service/controllers/boutiqueOptionsController.js`
   - Complete CRUD operations
   - Search and pagination logic
   - Error handling and validation

3. **Routes**: `packages/production-service/routes/boutiqueOptionsRoutes.js`
   - RESTful API endpoints
   - Input validation middleware
   - Authentication middleware

4. **App Integration**: Updated `packages/production-service/app.js`
   - Added route registration

### Frontend
1. **HTML Demo**: `packages/production-service/public/boutique-options.html`
   - Complete working interface
   - Bootstrap styling
   - AJAX functionality

2. **Vue.js Component**: `packages/production-service/vue-components/BoutiqueOptionsManager.vue`
   - Reusable Vue component
   - Event-driven architecture
   - Responsive design

## Setup Instructions

### 1. Database Setup
Create the `boutique_options` table in your database:

```sql
CREATE TABLE boutique_options (
    id INT(10) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    UNIQUE KEY unique_name (name)
);
```

### 2. Backend Setup
The backend files are already integrated into the production-service. Make sure:

1. The production-service is running
2. Database connection is configured
3. Authentication middleware is working

### 3. Frontend Integration

#### Option A: Use HTML Demo
1. Copy `public/boutique-options.html` to your web server
2. Update the `API_BASE_URL` constant to match your server
3. Access the page in your browser

#### Option B: Integrate Vue Component
1. Copy `vue-components/BoutiqueOptionsManager.vue` to your Vue.js project
2. Import and register the component:

```javascript
import BoutiqueOptionsManager from './components/BoutiqueOptionsManager.vue';

export default {
  components: {
    BoutiqueOptionsManager
  }
}
```

3. Use in your template:

```vue
<template>
  <BoutiqueOptionsManager 
    @success="handleSuccess"
    @error="handleError"
  />
</template>
```

### 4. Sidebar Integration
To add to your existing sidebar, add this link:

```vue
<router-link to="/boutique-options" class="nav-link">
  <i class="fas fa-cogs"></i>
  <span>Product Options</span>
</router-link>
```

## Features

### ✅ Complete CRUD Operations
- Create new options
- Read/List options with pagination
- Update existing options
- Delete options

### ✅ Search & Filter
- Search by option name
- Sort by name, price, or date
- Ascending/descending order

### ✅ Pagination
- Configurable page size
- Navigation controls
- Item count display

### ✅ Validation
- Required field validation
- Unique name constraint
- Price range validation
- Input sanitization

### ✅ User Experience
- Loading states
- Error handling
- Success feedback
- Responsive design

### ✅ Security
- Authentication required
- Input validation
- SQL injection protection
- XSS prevention

## API Response Format

### Success Response
```json
{
  "success": true,
  "data": {
    "options": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  },
  "message": "Options retrieved successfully"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## Testing

### Manual Testing
1. Start the production-service
2. Open the HTML demo page
3. Test all CRUD operations
4. Verify search and pagination

### API Testing
Use tools like Postman or curl to test the API endpoints:

```bash
# Get all options
curl -X GET "http://localhost:3000/api/production/boutique-options"

# Create new option
curl -X POST "http://localhost:3000/api/production/boutique-options" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Option","price":99.99}'

# Search options
curl -X GET "http://localhost:3000/api/production/boutique-options?search=test&page=1&limit=10"
```

## Customization

### Adding Fields
To add new fields to the boutique options:

1. Update the database table
2. Modify the Sequelize model
3. Update validation schemas
4. Modify frontend forms

### Styling
The Vue component uses Bootstrap classes. Customize by:

1. Modifying the scoped CSS
2. Overriding Bootstrap variables
3. Adding custom classes

### Business Logic
Add custom business logic in the controller methods or create service classes for complex operations.

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure database is running and configured
2. **Authentication**: Check if auth middleware is properly configured
3. **CORS**: Configure CORS if frontend and backend are on different domains
4. **Validation Errors**: Check request body format and required fields

### Debug Mode
Enable debug logging in the controller to troubleshoot issues:

```javascript
const logger = require('sparsh-node-logger');
logger.debug('Debug message', { data: someData });
```
