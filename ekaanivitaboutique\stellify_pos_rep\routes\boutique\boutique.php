<?php
use App\Http\Controllers\API\BoutiqueOrdersController;
use App\Http\Controllers\API\ProductOptionsController;
use Illuminate\Support\Facades\Route;

// If you want to keep the old routes for reference, comment them out properly:
//
// /*
// Route::middleware(['auth'])->group(function () {
//     // Existing boutique routes
//     Route::get('boutiquesorder', [BoutiqueController::class, 'orderIndex']);
//     // ...other routes...
// });
// */


Route::get('/boutiqueorders', [BoutiqueOrdersController::class, 'index'])->name('boutiqueorders.index');

Route::prefix('boutique-orders')->group(function () {
    Route::get('/', [BoutiqueOrdersController::class, 'index'])->name('boutique-orders.index');
    Route::get('/create', [BoutiqueOrdersController::class, 'create'])->name('boutique-orders.create');
    Route::post('/', [BoutiqueOrdersController::class, 'store'])->name('boutique-orders.store');

    Route::post('boutique-orders/create-customer', [BoutiqueOrdersController::class, 'createCustomer'])->name('boutique-orders.create-customer');
    Route::post('boutique-orders/create-option', [BoutiqueOrdersController::class, 'createOption'])->name('boutique-orders.create-option');

    Route::get('/measurement/{id}', [BoutiqueOrdersController::class, 'measurement'])->name('boutique-orders.measurement');
    Route::post('/measurement{id}', [BoutiqueOrdersController::class, 'measurementUpload'])->name('boutique-orders.measurement.upload');
    Route::get('/fabric/{id}', [BoutiqueOrdersController::class, 'fabric'])->name('boutique-orders.fabric');
    Route::get('/shipping', [BoutiqueOrdersController::class, 'shipping'])->name('boutique-orders.shipping');
    Route::get('/delivery/{id}', [BoutiqueOrdersController::class, 'delivery'])->name('boutique-orders.delivery');
    Route::post('/delivery/{id}', [BoutiqueOrdersController::class, 'deliveryUpload'])->name('boutique-orders.delivery.upload');
    Route::post('/fabric/{id}', [BoutiqueOrdersController::class, 'fabricUpload'])->name('boutique-orders.fabric.upload');
    Route::get('/payment/{id}', [BoutiqueOrdersController::class, 'payment'])->name('boutique-orders.payment');
    Route::post('/payment/{id}', [BoutiqueOrdersController::class, 'paymentUpdate'])->name('boutique-orders.payment.update');

    // Order action routes (must be before generic /{id} routes)
    Route::post('/{id}/cancel', [BoutiqueOrdersController::class, 'cancelOrder'])->name('boutique-orders.cancel');
    Route::post('/{id}/ready-to-ship', [BoutiqueOrdersController::class, 'readyToShip'])->name('boutique-orders.ready-to-ship');
    Route::post('/{id}/change-delivery-date', [BoutiqueOrdersController::class, 'changeDeliveryDate'])->name('boutique-orders.change-delivery-date');

    // Generic CRUD routes
    Route::get('/{id}/edit', [BoutiqueOrdersController::class, 'edit'])->name('boutique-orders.edit');
    Route::put('/{id}', [BoutiqueOrdersController::class, 'update'])->name('boutique-orders.update');
    Route::delete('/{id}', [BoutiqueOrdersController::class, 'destroy'])->name('boutique-orders.destroy');

    // Share and PDF routes
    Route::get('/{id}/pdf', [BoutiqueOrdersController::class, 'generatePDF'])->name('boutique-orders.pdf');
    Route::get('/{id}/store-pdf', [BoutiqueOrdersController::class, 'generateAndStorePDF'])->name('boutique-orders.store-pdf');
    Route::get('/{id}/whatsapp-message', [BoutiqueOrdersController::class, 'getWhatsAppMessage'])->name('boutique-orders.whatsapp-message');
    Route::post('/{id}/share-whatsapp', [BoutiqueOrdersController::class, 'shareWhatsApp'])->name('boutique-orders.share-whatsapp');
    Route::post('/{id}/share-email', [BoutiqueOrdersController::class, 'shareEmail'])->name('boutique-orders.share-email');

    Route::get('/{id}', [BoutiqueOrdersController::class, 'show'])->name('boutique-orders.show');

    // Attachment management routes
    Route::delete('/attachment/{attachmentId}', [BoutiqueOrdersController::class, 'deleteAttachment'])->name('boutique-orders.delete-attachment');

});

// Product Options Routes
Route::prefix('product-options')->group(function () {
    Route::get('/', [ProductOptionsController::class, 'index'])->name('product-options.index');
    Route::get('/create', [ProductOptionsController::class, 'create'])->name('product-options.create');
    Route::post('/', [ProductOptionsController::class, 'store'])->name('product-options.store');
    Route::get('/{id}/edit', [ProductOptionsController::class, 'edit'])->name('product-options.edit');
    Route::put('/{id}', [ProductOptionsController::class, 'update'])->name('product-options.update');
    Route::delete('/{id}', [ProductOptionsController::class, 'destroy'])->name('product-options.destroy');
});

