<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boutique Options Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-box {
            max-width: 400px;
        }
        .table-actions {
            white-space: nowrap;
        }
        .price-display {
            font-weight: bold;
            color: #28a745;
        }
        .loading {
            display: none;
        }
        .pagination-info {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-cogs"></i> Boutique Options Management</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#optionModal" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i> Add New Option
                    </button>
                </div>

                <!-- Search and Filter Section -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group search-box">
                                    <input type="text" class="form-control" id="searchInput" placeholder="Search by name...">
                                    <button class="btn btn-outline-secondary" type="button" onclick="searchOptions()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <select class="form-select d-inline-block w-auto me-2" id="sortBy">
                                    <option value="name">Sort by Name</option>
                                    <option value="price">Sort by Price</option>
                                    <option value="created_at">Sort by Created Date</option>
                                </select>
                                <select class="form-select d-inline-block w-auto" id="sortOrder">
                                    <option value="ASC">Ascending</option>
                                    <option value="DESC">Descending</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Options Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="loading text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Loading...</p>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="optionsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Created Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="optionsTableBody">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="pagination-info" id="paginationInfo">
                                <!-- Pagination info will be displayed here -->
                            </div>
                            <nav>
                                <ul class="pagination mb-0" id="pagination">
                                    <!-- Pagination buttons will be generated here -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Option Modal -->
    <div class="modal fade" id="optionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Add New Option</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="optionForm">
                        <div class="mb-3">
                            <label for="optionName" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="optionName" required maxlength="255">
                            <div class="invalid-feedback" id="nameError"></div>
                        </div>
                        <div class="mb-3">
                            <label for="optionPrice" class="form-label">Price (₹) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="optionPrice" step="0.01" min="0" max="999999.99" required>
                            <div class="invalid-feedback" id="priceError"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveOption()" id="saveButton">
                        <i class="fas fa-save"></i> Save
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let currentPage = 1;
        let currentLimit = 10;
        let currentSearch = '';
        let currentSortBy = 'name';
        let currentSortOrder = 'ASC';
        let editingOptionId = null;
        
        // API base URL - adjust this to match your server
        const API_BASE_URL = '/api/production/boutique-options';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadOptions();
            
            // Event listeners
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchOptions();
                }
            });
            
            document.getElementById('sortBy').addEventListener('change', loadOptions);
            document.getElementById('sortOrder').addEventListener('change', loadOptions);
        });

        // Load options from API
        async function loadOptions(page = 1) {
            try {
                showLoading(true);
                currentPage = page;
                currentSortBy = document.getElementById('sortBy').value;
                currentSortOrder = document.getElementById('sortOrder').value;
                
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: currentLimit,
                    search: currentSearch,
                    sortBy: currentSortBy,
                    sortOrder: currentSortOrder
                });

                const response = await fetch(`${API_BASE_URL}?${params}`);
                const data = await response.json();

                if (data.success) {
                    displayOptions(data.data.options);
                    displayPagination(data.data.pagination);
                } else {
                    showError('Failed to load options: ' + data.message);
                }
            } catch (error) {
                showError('Error loading options: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // Display options in table
        function displayOptions(options) {
            const tbody = document.getElementById('optionsTableBody');
            tbody.innerHTML = '';

            if (options.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>No options found</p>
                        </td>
                    </tr>
                `;
                return;
            }

            options.forEach(option => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${option.id}</td>
                    <td>${escapeHtml(option.name)}</td>
                    <td class="price-display">₹${parseFloat(option.price).toFixed(2)}</td>
                    <td>${new Date(option.createdAt).toLocaleDateString()}</td>
                    <td class="table-actions">
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editOption(${option.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteOption(${option.id}, '${escapeHtml(option.name)}')" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Display pagination
        function displayPagination(pagination) {
            const paginationInfo = document.getElementById('paginationInfo');
            const paginationNav = document.getElementById('pagination');
            
            // Update info
            const start = ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1;
            const end = Math.min(start + pagination.itemsPerPage - 1, pagination.totalItems);
            paginationInfo.textContent = `Showing ${start}-${end} of ${pagination.totalItems} items`;
            
            // Update pagination buttons
            paginationNav.innerHTML = '';
            
            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${!pagination.hasPrevPage ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadOptions(${pagination.currentPage - 1})">Previous</a>`;
            paginationNav.appendChild(prevLi);
            
            // Page numbers
            const startPage = Math.max(1, pagination.currentPage - 2);
            const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === pagination.currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="loadOptions(${i})">${i}</a>`;
                paginationNav.appendChild(li);
            }
            
            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${!pagination.hasNextPage ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadOptions(${pagination.currentPage + 1})">Next</a>`;
            paginationNav.appendChild(nextLi);
        }

        // Search options
        function searchOptions() {
            currentSearch = document.getElementById('searchInput').value.trim();
            loadOptions(1);
        }

        // Open create modal
        function openCreateModal() {
            editingOptionId = null;
            document.getElementById('modalTitle').textContent = 'Add New Option';
            document.getElementById('optionForm').reset();
            clearValidationErrors();
        }

        // Edit option
        async function editOption(id) {
            try {
                const response = await fetch(`${API_BASE_URL}/${id}`);
                const data = await response.json();

                if (data.success) {
                    editingOptionId = id;
                    document.getElementById('modalTitle').textContent = 'Edit Option';
                    document.getElementById('optionName').value = data.data.name;
                    document.getElementById('optionPrice').value = data.data.price;
                    clearValidationErrors();
                    
                    const modal = new bootstrap.Modal(document.getElementById('optionModal'));
                    modal.show();
                } else {
                    showError('Failed to load option: ' + data.message);
                }
            } catch (error) {
                showError('Error loading option: ' + error.message);
            }
        }

        // Save option (create or update)
        async function saveOption() {
            try {
                const name = document.getElementById('optionName').value.trim();
                const price = parseFloat(document.getElementById('optionPrice').value);

                if (!name) {
                    showValidationError('nameError', 'Name is required');
                    return;
                }

                if (isNaN(price) || price < 0) {
                    showValidationError('priceError', 'Valid price is required');
                    return;
                }

                const saveButton = document.getElementById('saveButton');
                saveButton.disabled = true;
                saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                const url = editingOptionId ? `${API_BASE_URL}/${editingOptionId}` : API_BASE_URL;
                const method = editingOptionId ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, price })
                });

                const data = await response.json();

                if (data.success) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('optionModal'));
                    modal.hide();
                    loadOptions(currentPage);
                    showSuccess(data.message);
                } else {
                    if (response.status === 409) {
                        showValidationError('nameError', data.message);
                    } else {
                        showError(data.message);
                    }
                }
            } catch (error) {
                showError('Error saving option: ' + error.message);
            } finally {
                const saveButton = document.getElementById('saveButton');
                saveButton.disabled = false;
                saveButton.innerHTML = '<i class="fas fa-save"></i> Save';
            }
        }

        // Delete option
        async function deleteOption(id, name) {
            if (!confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/${id}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.success) {
                    loadOptions(currentPage);
                    showSuccess(data.message);
                } else {
                    showError('Failed to delete option: ' + data.message);
                }
            } catch (error) {
                showError('Error deleting option: ' + error.message);
            }
        }

        // Utility functions
        function showLoading(show) {
            const loading = document.querySelector('.loading');
            const table = document.getElementById('optionsTable');
            
            if (show) {
                loading.style.display = 'block';
                table.style.display = 'none';
            } else {
                loading.style.display = 'none';
                table.style.display = 'table';
            }
        }

        function showError(message) {
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            alert('Success: ' + message);
        }

        function showValidationError(fieldId, message) {
            const field = document.getElementById(fieldId);
            field.textContent = message;
            field.style.display = 'block';
            
            const input = field.previousElementSibling;
            input.classList.add('is-invalid');
        }

        function clearValidationErrors() {
            document.querySelectorAll('.invalid-feedback').forEach(el => {
                el.style.display = 'none';
                el.textContent = '';
            });
            
            document.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
