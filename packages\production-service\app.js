const express = require('express');
const app = express();
const authMiddleware = require('./middleware/auth');
const customerRoutes = require('./routes/customerRoutes');
const customerstoreRoutes = require('./routes/customerStoreRoutes');
const customerNotesRoutes = require('./routes/customerNotesRoutes');
const vendorRoutes = require('./routes/vendorRoutes');
const vendorNotesRoutes = require('./routes/vendorNotesRoutes');
const boutiqueOptionsRoutes = require('./routes/boutiqueOptionsRoutes');
//const validateInput = require('./middleware/validateInput');

app.use(express.json());
//app.use(validateInput); // Global check
app.use(authMiddleware);
app.use('/api/production/customers', customerRoutes);
app.use('/api/production/customerstores', customerstoreRoutes);
app.use('/api/production/customernotes', customerNotesRoutes);
app.use('/api/production/vendors', vendorRoutes);
app.use('/api/production/vendornotes', vendorNotesRoutes);
app.use('/api/production/boutique-options', boutiqueOptionsRoutes);

app.use('*', (req, res) => {
  res.status(404).json({
    statusCode: 404,
    success: "FAILURE",
    message: `Route ${req.method} ${req.originalUrl} not found`,
    errorDetails: [{ message: `The requested endpoint does not exist` }]
  });
});

// Error handling middleware (should be last)
const errorMiddleware = require('./middleware/errorMiddleware');
app.use(errorMiddleware);

module.exports = app;
