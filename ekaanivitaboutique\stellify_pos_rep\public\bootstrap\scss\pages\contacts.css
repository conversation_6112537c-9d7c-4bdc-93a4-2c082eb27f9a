.card-list-container .card.avatar-card .card-img-top {
  height: 17rem; }
@media (min-width: 1600px) {
  .card-list-container .card-item {
    flex: 0 0 20% !important; } }
@media (min-width: 1400px) and (max-width: 1599px) {
  .card-list-container .card-item {
    flex: 0 0 25%; } }
@media (min-width: 992px) and (max-width: 1399px) {
  .card-list-container .card-item {
    flex: 0 0 33.33%;
    max-width: 33.33%; } }
@media (min-width: 576px) and (max-width: 680px) {
  .card-list-container .card-item {
    flex: 0 0 100%;
    max-width: 100%; } }

/*# sourceMappingURL=contacts.css.map */
