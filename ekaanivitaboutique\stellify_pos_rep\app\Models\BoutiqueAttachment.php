<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class BoutiqueAttachment extends Model
{
    protected $fillable = ['order_id', 'attachmentname', 'attachment', 'type'];

    public static function storeOrUpdateAttachment($order_id, $type, $file)
    {
        if (!$file || !$file->isValid()) {
            throw new \Exception('Invalid file upload.');
        }

        $existing = self::where('order_id', $order_id)->where('type', $type)->first();
        $filename = uniqid() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs('attachments', $filename, 'public');

        if ($existing) {
            if ($existing->attachment && Storage::disk('public')->exists($existing->attachment)) {
                Storage::disk('public')->delete($existing->attachment);
            }
            $existing->attachmentname = $file->getClientOriginalName();
            $existing->attachment = $path;
            $existing->type = $type;
            $existing->save();
            return $existing;
        } else {
            return self::create([
                'order_id' => $order_id,
                'attachmentname' => $file->getClientOriginalName(),
                'attachment' => $path,
                'type' => $type,
            ]);
        }
    }
}