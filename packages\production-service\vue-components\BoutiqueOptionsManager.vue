<template>
  <div class="boutique-options-manager">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="fas fa-cogs"></i> Boutique Options</h2>
      <button 
        class="btn btn-primary" 
        @click="openCreateModal"
        data-bs-toggle="modal" 
        data-bs-target="#optionModal"
      >
        <i class="fas fa-plus"></i> Add New Option
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="input-group">
              <input 
                type="text" 
                class="form-control" 
                v-model="searchQuery"
                @keyup.enter="searchOptions"
                placeholder="Search by name..."
              >
              <button class="btn btn-outline-secondary" @click="searchOptions">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
          <div class="col-md-6 text-end">
            <select class="form-select d-inline-block w-auto me-2" v-model="sortBy" @change="loadOptions">
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
              <option value="created_at">Sort by Created Date</option>
            </select>
            <select class="form-select d-inline-block w-auto" v-model="sortOrder" @change="loadOptions">
              <option value="ASC">Ascending</option>
              <option value="DESC">Descending</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Options Table -->
    <div class="card">
      <div class="card-body">
        <!-- Loading -->
        <div v-if="loading" class="text-center py-4">
          <i class="fas fa-spinner fa-spin fa-2x"></i>
          <p class="mt-2">Loading...</p>
        </div>

        <!-- Table -->
        <div v-else class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Price</th>
                <th>Created Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="options.length === 0">
                <td colspan="5" class="text-center text-muted py-4">
                  <i class="fas fa-inbox fa-2x mb-2"></i>
                  <p>No options found</p>
                </td>
              </tr>
              <tr v-for="option in options" :key="option.id">
                <td>{{ option.id }}</td>
                <td>{{ option.name }}</td>
                <td class="price-display">₹{{ parseFloat(option.price).toFixed(2) }}</td>
                <td>{{ formatDate(option.createdAt) }}</td>
                <td class="table-actions">
                  <button 
                    class="btn btn-sm btn-outline-primary me-1" 
                    @click="editOption(option)"
                    title="Edit"
                  >
                    <i class="fas fa-edit"></i>
                  </button>
                  <button 
                    class="btn btn-sm btn-outline-danger" 
                    @click="deleteOption(option)"
                    title="Delete"
                  >
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center mt-3" v-if="pagination.totalPages > 1">
          <div class="pagination-info">
            Showing {{ paginationStart }}-{{ paginationEnd }} of {{ pagination.totalItems }} items
          </div>
          <nav>
            <ul class="pagination mb-0">
              <li class="page-item" :class="{ disabled: !pagination.hasPrevPage }">
                <a class="page-link" href="#" @click.prevent="loadOptions(pagination.currentPage - 1)">Previous</a>
              </li>
              <li 
                v-for="page in visiblePages" 
                :key="page"
                class="page-item" 
                :class="{ active: page === pagination.currentPage }"
              >
                <a class="page-link" href="#" @click.prevent="loadOptions(page)">{{ page }}</a>
              </li>
              <li class="page-item" :class="{ disabled: !pagination.hasNextPage }">
                <a class="page-link" href="#" @click.prevent="loadOptions(pagination.currentPage + 1)">Next</a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- Option Modal -->
    <div class="modal fade" id="optionModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ isEditing ? 'Edit Option' : 'Add New Option' }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveOption">
              <div class="mb-3">
                <label for="optionName" class="form-label">Name <span class="text-danger">*</span></label>
                <input 
                  type="text" 
                  class="form-control" 
                  id="optionName"
                  v-model="form.name"
                  :class="{ 'is-invalid': errors.name }"
                  maxlength="255"
                  required
                >
                <div class="invalid-feedback" v-if="errors.name">{{ errors.name }}</div>
              </div>
              <div class="mb-3">
                <label for="optionPrice" class="form-label">Price (₹) <span class="text-danger">*</span></label>
                <input 
                  type="number" 
                  class="form-control" 
                  id="optionPrice"
                  v-model="form.price"
                  :class="{ 'is-invalid': errors.price }"
                  step="0.01" 
                  min="0" 
                  max="999999.99"
                  required
                >
                <div class="invalid-feedback" v-if="errors.price">{{ errors.price }}</div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button 
              type="button" 
              class="btn btn-primary" 
              @click="saveOption"
              :disabled="saving"
            >
              <i class="fas" :class="saving ? 'fa-spinner fa-spin' : 'fa-save'"></i>
              {{ saving ? 'Saving...' : 'Save' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BoutiqueOptionsManager',
  data() {
    return {
      options: [],
      loading: false,
      saving: false,
      searchQuery: '',
      sortBy: 'name',
      sortOrder: 'ASC',
      currentPage: 1,
      itemsPerPage: 10,
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10,
        hasNextPage: false,
        hasPrevPage: false
      },
      isEditing: false,
      editingId: null,
      form: {
        name: '',
        price: 0
      },
      errors: {}
    }
  },
  computed: {
    paginationStart() {
      return ((this.pagination.currentPage - 1) * this.pagination.itemsPerPage) + 1;
    },
    paginationEnd() {
      return Math.min(this.paginationStart + this.pagination.itemsPerPage - 1, this.pagination.totalItems);
    },
    visiblePages() {
      const start = Math.max(1, this.pagination.currentPage - 2);
      const end = Math.min(this.pagination.totalPages, this.pagination.currentPage + 2);
      const pages = [];
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },
  mounted() {
    this.loadOptions();
  },
  methods: {
    async loadOptions(page = 1) {
      try {
        this.loading = true;
        this.currentPage = page;
        
        const params = new URLSearchParams({
          page: this.currentPage,
          limit: this.itemsPerPage,
          search: this.searchQuery,
          sortBy: this.sortBy,
          sortOrder: this.sortOrder
        });

        const response = await fetch(`/api/production/boutique-options?${params}`);
        const data = await response.json();

        if (data.success) {
          this.options = data.data.options;
          this.pagination = data.data.pagination;
        } else {
          this.$emit('error', 'Failed to load options: ' + data.message);
        }
      } catch (error) {
        this.$emit('error', 'Error loading options: ' + error.message);
      } finally {
        this.loading = false;
      }
    },

    searchOptions() {
      this.loadOptions(1);
    },

    openCreateModal() {
      this.isEditing = false;
      this.editingId = null;
      this.form = { name: '', price: 0 };
      this.errors = {};
    },

    editOption(option) {
      this.isEditing = true;
      this.editingId = option.id;
      this.form = { 
        name: option.name, 
        price: parseFloat(option.price) 
      };
      this.errors = {};
      
      // Show modal
      const modal = new bootstrap.Modal(document.getElementById('optionModal'));
      modal.show();
    },

    async saveOption() {
      try {
        this.saving = true;
        this.errors = {};

        // Validation
        if (!this.form.name.trim()) {
          this.errors.name = 'Name is required';
          return;
        }

        if (isNaN(this.form.price) || this.form.price < 0) {
          this.errors.price = 'Valid price is required';
          return;
        }

        const url = this.isEditing 
          ? `/api/production/boutique-options/${this.editingId}`
          : '/api/production/boutique-options';
        
        const method = this.isEditing ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: this.form.name.trim(),
            price: parseFloat(this.form.price)
          })
        });

        const data = await response.json();

        if (data.success) {
          // Hide modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('optionModal'));
          modal.hide();
          
          // Reload options
          this.loadOptions(this.currentPage);
          
          // Emit success event
          this.$emit('success', data.message);
        } else {
          if (response.status === 409) {
            this.errors.name = data.message;
          } else {
            this.$emit('error', data.message);
          }
        }
      } catch (error) {
        this.$emit('error', 'Error saving option: ' + error.message);
      } finally {
        this.saving = false;
      }
    },

    async deleteOption(option) {
      if (!confirm(`Are you sure you want to delete "${option.name}"? This action cannot be undone.`)) {
        return;
      }

      try {
        const response = await fetch(`/api/production/boutique-options/${option.id}`, {
          method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
          this.loadOptions(this.currentPage);
          this.$emit('success', data.message);
        } else {
          this.$emit('error', 'Failed to delete option: ' + data.message);
        }
      } catch (error) {
        this.$emit('error', 'Error deleting option: ' + error.message);
      }
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString();
    }
  }
}
</script>

<style scoped>
.price-display {
  font-weight: bold;
  color: #28a745;
}

.table-actions {
  white-space: nowrap;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.9em;
}
</style>
