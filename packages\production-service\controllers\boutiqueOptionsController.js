const { BoutiqueOptionModel } = require('../models/BoutiqueOption');
const { Op } = require('sequelize');
const logger = require('sparsh-node-logger');

class BoutiqueOptionsController {
  /**
   * Get all boutique options with pagination and search
   */
  static async getAllOptions(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        sortBy = 'name',
        sortOrder = 'ASC'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);
      const whereClause = {};

      // Add search functionality
      if (search && search.trim()) {
        whereClause.name = {
          [Op.iLike]: `%${search.trim()}%`
        };
      }

      const { count, rows } = await BoutiqueOptionModel.findAndCountAll({
        where: whereClause,
        limit: parseInt(limit),
        offset: offset,
        order: [[sortBy, sortOrder.toUpperCase()]],
      });

      const totalPages = Math.ceil(count / parseInt(limit));

      res.status(200).json({
        success: true,
        data: {
          options: rows,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: count,
            itemsPerPage: parseInt(limit),
            hasNextPage: parseInt(page) < totalPages,
            hasPrevPage: parseInt(page) > 1
          }
        },
        message: 'Boutique options retrieved successfully'
      });
    } catch (error) {
      logger.error('Error fetching boutique options:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch boutique options',
        error: error.message
      });
    }
  }

  /**
   * Get a single boutique option by ID
   */
  static async getOptionById(req, res) {
    try {
      const { id } = req.params;
      
      const option = await BoutiqueOptionModel.findByPk(id);
      
      if (!option) {
        return res.status(404).json({
          success: false,
          message: 'Boutique option not found'
        });
      }

      res.status(200).json({
        success: true,
        data: option,
        message: 'Boutique option retrieved successfully'
      });
    } catch (error) {
      logger.error('Error fetching boutique option:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch boutique option',
        error: error.message
      });
    }
  }

  /**
   * Create a new boutique option
   */
  static async createOption(req, res) {
    try {
      const { name, price } = req.body;

      // Validation
      if (!name || !name.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Name is required'
        });
      }

      if (price === undefined || price === null || isNaN(price)) {
        return res.status(400).json({
          success: false,
          message: 'Valid price is required'
        });
      }

      if (parseFloat(price) < 0) {
        return res.status(400).json({
          success: false,
          message: 'Price cannot be negative'
        });
      }

      // Check if name already exists
      const existingOption = await BoutiqueOptionModel.findOne({
        where: {
          name: {
            [Op.iLike]: name.trim()
          }
        }
      });

      if (existingOption) {
        return res.status(409).json({
          success: false,
          message: 'An option with this name already exists'
        });
      }

      const newOption = await BoutiqueOptionModel.create({
        name: name.trim(),
        price: parseFloat(price)
      });

      res.status(201).json({
        success: true,
        data: newOption,
        message: 'Boutique option created successfully'
      });
    } catch (error) {
      logger.error('Error creating boutique option:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create boutique option',
        error: error.message
      });
    }
  }

  /**
   * Update a boutique option
   */
  static async updateOption(req, res) {
    try {
      const { id } = req.params;
      const { name, price } = req.body;

      const option = await BoutiqueOptionModel.findByPk(id);
      
      if (!option) {
        return res.status(404).json({
          success: false,
          message: 'Boutique option not found'
        });
      }

      // Validation
      if (!name || !name.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Name is required'
        });
      }

      if (price === undefined || price === null || isNaN(price)) {
        return res.status(400).json({
          success: false,
          message: 'Valid price is required'
        });
      }

      if (parseFloat(price) < 0) {
        return res.status(400).json({
          success: false,
          message: 'Price cannot be negative'
        });
      }

      // Check if name already exists (excluding current option)
      const existingOption = await BoutiqueOptionModel.findOne({
        where: {
          name: {
            [Op.iLike]: name.trim()
          },
          id: {
            [Op.ne]: id
          }
        }
      });

      if (existingOption) {
        return res.status(409).json({
          success: false,
          message: 'An option with this name already exists'
        });
      }

      await option.update({
        name: name.trim(),
        price: parseFloat(price)
      });

      res.status(200).json({
        success: true,
        data: option,
        message: 'Boutique option updated successfully'
      });
    } catch (error) {
      logger.error('Error updating boutique option:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update boutique option',
        error: error.message
      });
    }
  }

  /**
   * Delete a boutique option
   */
  static async deleteOption(req, res) {
    try {
      const { id } = req.params;
      
      const option = await BoutiqueOptionModel.findByPk(id);
      
      if (!option) {
        return res.status(404).json({
          success: false,
          message: 'Boutique option not found'
        });
      }

      await option.destroy();

      res.status(200).json({
        success: true,
        message: 'Boutique option deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting boutique option:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete boutique option',
        error: error.message
      });
    }
  }

  /**
   * Search boutique options by name
   */
  static async searchOptions(req, res) {
    try {
      const { q: searchTerm, limit = 10 } = req.query;

      if (!searchTerm || !searchTerm.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Search term is required'
        });
      }

      const options = await BoutiqueOptionModel.findAll({
        where: {
          name: {
            [Op.iLike]: `%${searchTerm.trim()}%`
          }
        },
        limit: parseInt(limit),
        order: [['name', 'ASC']]
      });

      res.status(200).json({
        success: true,
        data: options,
        message: 'Search completed successfully'
      });
    } catch (error) {
      logger.error('Error searching boutique options:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search boutique options',
        error: error.message
      });
    }
  }
}

module.exports = BoutiqueOptionsController;
