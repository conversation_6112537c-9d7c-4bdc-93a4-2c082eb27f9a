<?php

namespace App\Services;

use App\Models\BoutiqueOrders;
use App\Models\Customer;
use App\Models\SmsTemplate;

class OrderNotificationService
{
    private $whatsAppService;

    public function __construct(WhatsAppService $whatsAppService)
    {
        $this->whatsAppService = $whatsAppService;
    }

    /**
     * Send order creation notification
     *
     * @param BoutiqueOrders $order
     * @return array
     */
    public function sendOrderCreatedNotification(BoutiqueOrders $order)
    {
        try {
            $customer = $order->customer;
            if (!$customer || !$customer->phone_number) {
                return [
                    'success' => false,
                    'message' => 'Customer phone number not found'
                ];
            }

            $message = $this->getOrderCreatedMessage($order, $customer);
            
            $result = $this->whatsAppService->sendMessage(
                $customer->phone_number,
                $message
            );

            // Don't log to avoid permission issues

            return $result;

        } catch (\Exception $e) {
            // Don't log to avoid permission issues

            return [
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send order cancellation notification
     *
     * @param BoutiqueOrders $order
     * @return array
     */
    public function sendOrderCancelledNotification(BoutiqueOrders $order)
    {
        try {
            $customer = $order->customer;
            if (!$customer || !$customer->phone_number) {
                return [
                    'success' => false,
                    'message' => 'Customer phone number not found'
                ];
            }

            $message = $this->getOrderCancelledMessage($order, $customer);
            
            $result = $this->whatsAppService->sendMessage(
                $customer->phone_number,
                $message
            );

            // Don't log to avoid permission issues

            return $result;

        } catch (\Exception $e) {
            // Don't log to avoid permission issues

            return [
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send order completion notification
     *
     * @param BoutiqueOrders $order
     * @return array
     */
    public function sendOrderCompletedNotification(BoutiqueOrders $order)
    {
        try {
            $customer = $order->customer;
            if (!$customer || !$customer->phone_number) {
                return [
                    'success' => false,
                    'message' => 'Customer phone number not found'
                ];
            }

            $message = $this->getOrderCompletedMessage($order, $customer);
            
            $result = $this->whatsAppService->sendMessage(
                $customer->phone_number,
                $message
            );

            // Don't log to avoid permission issues

            return $result;

        } catch (\Exception $e) {
            // Don't log to avoid permission issues

            return [
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send order shipping notification
     *
     * @param BoutiqueOrders $order
     * @return array
     */
    public function sendOrderShippingNotification(BoutiqueOrders $order)
    {
        try {
            $customer = $order->customer;
            if (!$customer || !$customer->phone_number) {
                return [
                    'success' => false,
                    'message' => 'Customer phone number not found'
                ];
            }

            $message = $this->getOrderShippingMessage($order, $customer);

            $result = $this->whatsAppService->sendMessage(
                $customer->phone_number,
                $message
            );

            // Don't log to avoid permission issues

            return $result;

        } catch (\Exception $e) {
            // Don't log to avoid permission issues

            return [
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send payment reminder notification
     *
     * @param BoutiqueOrders $order
     * @return array
     */
    public function sendPaymentReminderNotification(BoutiqueOrders $order)
    {
        try {
            $customer = $order->customer;
            if (!$customer || !$customer->phone_number) {
                return [
                    'success' => false,
                    'message' => 'Customer phone number not found'
                ];
            }

            // Only send if there's a balance due
            if ($order->balance_amount <= 0) {
                return [
                    'success' => false,
                    'message' => 'No balance due for this order'
                ];
            }

            $message = $this->getPaymentReminderMessage($order, $customer);

            $result = $this->whatsAppService->sendMessage(
                $customer->phone_number,
                $message
            );

            // Don't log to avoid permission issues

            return $result;

        } catch (\Exception $e) {
            // Don't log to avoid permission issues

            return [
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send order status update notification
     *
     * @param BoutiqueOrders $order
     * @param string $oldStatus
     * @param string $newStatus
     * @return array
     */
    public function sendOrderStatusUpdateNotification(BoutiqueOrders $order, $oldStatus, $newStatus)
    {
        try {
            $customer = $order->customer;
            if (!$customer || !$customer->phone_number) {
                return [
                    'success' => false,
                    'message' => 'Customer phone number not found'
                ];
            }

            // Send specific notification based on new status
            if ($newStatus === 'Shipping') {
                return $this->sendOrderShippingNotification($order);
            }

            $message = $this->getOrderStatusUpdateMessage($order, $customer, $oldStatus, $newStatus);

            $result = $this->whatsAppService->sendMessage(
                $customer->phone_number,
                $message
            );

            // Don't log to avoid permission issues

            return $result;

        } catch (\Exception $e) {
            // Don't log to avoid permission issues

            return [
                'success' => false,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get order created message template
     *
     * @param BoutiqueOrders $order
     * @param Customer $customer
     * @return string
     */
    private function getOrderCreatedMessage(BoutiqueOrders $order, Customer $customer)
    {
        $template = $this->getTemplate('order_created');
        
        if ($template) {
            return $this->replaceTemplateVariables($template, $order, $customer);
        }

        // Default message if no template found
        return "Dear {$customer->first_name},\n\n" .
               "Your order #{$order->id} has been successfully created at Ekaanivita Boutique.\n\n" .
               "Order Details:\n" .
               "- Delivery Date: " . \Carbon\Carbon::parse($order->delivery_date)->format('d-m-Y') . "\n" .
               "- Total Amount: ₹" . number_format($order->total, 2) . "\n" .
               "- Advance Paid: ₹" . number_format($order->advance_paid, 2) . "\n" .
               "- Balance: ₹" . number_format($order->balance_amount, 2) . "\n\n" .
               "Thank you for choosing Ekaanivita Boutique!\n\n" .
               "Contact: +91 81259 04154\n" .
               "Email: <EMAIL>";
    }

    /**
     * Get order cancelled message template
     *
     * @param BoutiqueOrders $order
     * @param Customer $customer
     * @return string
     */
    private function getOrderCancelledMessage(BoutiqueOrders $order, Customer $customer)
    {
        $template = $this->getTemplate('order_cancelled');
        
        if ($template) {
            return $this->replaceTemplateVariables($template, $order, $customer);
        }

        // Default message if no template found
        return "Dear {$customer->first_name},\n\n" .
               "We regret to inform you that your order #{$order->id} has been cancelled.\n\n" .
               "Reason: " . ($order->cancellation_reason ?? 'Not specified') . "\n\n" .
               "If you have any questions, please contact us.\n\n" .
               "Contact: +91 81259 04154\n" .
               "Email: <EMAIL>\n\n" .
               "Thank you for your understanding.\n" .
               "Ekaanivita Boutique";
    }

    /**
     * Get order completed message template
     *
     * @param BoutiqueOrders $order
     * @param Customer $customer
     * @return string
     */
    private function getOrderCompletedMessage(BoutiqueOrders $order, Customer $customer)
    {
        $template = $this->getTemplate('order_completed');
        
        if ($template) {
            return $this->replaceTemplateVariables($template, $order, $customer);
        }

        // Default message if no template found
        return "Dear {$customer->first_name},\n\n" .
               "Great news! Your order #{$order->id} has been completed and is ready for delivery.\n\n" .
               "Order Details:\n" .
               "- Total Amount: ₹" . number_format($order->total, 2) . "\n" .
               "- Amount Paid: ₹" . number_format($order->advance_paid, 2) . "\n" .
               ($order->balance_amount > 0 ? "- Balance Due: ₹" . number_format($order->balance_amount, 2) . "\n" : "") .
               "\nThank you for choosing Ekaanivita Boutique!\n\n" .
               "Contact: +91 81259 04154\n" .
               "Email: <EMAIL>";
    }

    /**
     * Get order shipping message template
     *
     * @param BoutiqueOrders $order
     * @param Customer $customer
     * @return string
     */
    private function getOrderShippingMessage(BoutiqueOrders $order, Customer $customer)
    {
        $template = $this->getTemplate('order_shipping');

        if ($template) {
            return $this->replaceTemplateVariables($template, $order, $customer);
        }

        // Default message if no template found
        return "Dear {$customer->first_name},\n\n" .
               "Your order #{$order->id} is now being shipped!\n\n" .
               "Expected Delivery Date: " . \Carbon\Carbon::parse($order->delivery_date)->format('d-m-Y') . "\n\n" .
               "We will notify you once your order is delivered.\n\n" .
               "Thank you for choosing Ekaanivita Boutique!\n\n" .
               "Contact: +91 81259 04154\n" .
               "Email: <EMAIL>";
    }

    /**
     * Get payment reminder message template
     *
     * @param BoutiqueOrders $order
     * @param Customer $customer
     * @return string
     */
    private function getPaymentReminderMessage(BoutiqueOrders $order, Customer $customer)
    {
        $template = $this->getTemplate('payment_reminder');

        if ($template) {
            return $this->replaceTemplateVariables($template, $order, $customer);
        }

        // Default message if no template found
        return "Dear {$customer->first_name},\n\n" .
               "This is a friendly reminder about the pending balance for your order #{$order->id}.\n\n" .
               "Order Details:\n" .
               "- Total Amount: ₹" . number_format($order->total, 2) . "\n" .
               "- Amount Paid: ₹" . number_format($order->advance_paid, 2) . "\n" .
               "- Balance Due: ₹" . number_format($order->balance_amount, 2) . "\n" .
               "- Delivery Date: " . \Carbon\Carbon::parse($order->delivery_date)->format('d-m-Y') . "\n\n" .
               "Please complete the payment before delivery.\n\n" .
               "Contact: +91 81259 04154\n" .
               "Email: <EMAIL>\n\n" .
               "Thank you!\n" .
               "Ekaanivita Boutique";
    }

    /**
     * Get order status update message template
     *
     * @param BoutiqueOrders $order
     * @param Customer $customer
     * @param string $oldStatus
     * @param string $newStatus
     * @return string
     */
    private function getOrderStatusUpdateMessage(BoutiqueOrders $order, Customer $customer, $oldStatus, $newStatus)
    {
        $template = $this->getTemplate('order_status_update');

        if ($template) {
            return $this->replaceTemplateVariables($template, $order, $customer);
        }

        // Default message if no template found
        return "Dear {$customer->first_name},\n\n" .
               "Your order #{$order->id} status has been updated.\n\n" .
               "Previous Status: {$oldStatus}\n" .
               "Current Status: {$newStatus}\n\n" .
               "Delivery Date: " . \Carbon\Carbon::parse($order->delivery_date)->format('d-m-Y') . "\n\n" .
               "Thank you for choosing Ekaanivita Boutique!\n\n" .
               "Contact: +91 81259 04154\n" .
               "Email: <EMAIL>";
    }

    /**
     * Get template from database
     *
     * @param string $templateType
     * @return string|null
     */
    private function getTemplate($templateType)
    {
        $template = SmsTemplate::where('template_type', $templateType)->first();
        
        if ($template) {
            return $template->custom_content ?: $template->default_content;
        }
        
        return null;
    }

    /**
     * Replace template variables with actual values
     *
     * @param string $template
     * @param BoutiqueOrders $order
     * @param Customer $customer
     * @return string
     */
    private function replaceTemplateVariables($template, BoutiqueOrders $order, Customer $customer)
    {
        $variables = [
            '{customer_name}' => $customer->first_name,
            '{customer_full_name}' => $customer->first_name . ' ' . $customer->last_name,
            '{order_id}' => $order->id,
            '{delivery_date}' => \Carbon\Carbon::parse($order->delivery_date)->format('d-m-Y'),
            '{total_amount}' => number_format($order->total, 2),
            '{advance_paid}' => number_format($order->advance_paid, 2),
            '{balance_amount}' => number_format($order->balance_amount, 2),
            '{order_status}' => $order->status,
            '{cancellation_reason}' => $order->cancellation_reason ?? 'Not specified',
            '{contact_phone}' => '+91 81259 04154',
            '{contact_email}' => '<EMAIL>',
            '{boutique_name}' => 'Ekaanivita Boutique'
        ];

        return str_replace(array_keys($variables), array_values($variables), $template);
    }

    /**
     * Check if notifications are enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return $this->whatsAppService->isConfigured();
    }
}
