<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\SmsTemplate;
use App\Models\Customer;
use App\Models\CustomerGroup;
use App\Services\WhatsAppService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WhatsAppController extends Controller
{
    private $whatsAppService;

    public function __construct(WhatsAppService $whatsAppService)
    {
        $this->whatsAppService = $whatsAppService;
    }

    /**
     * Get all WhatsApp templates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllTemplates()
    {
        try {
            $templates = SmsTemplate::whereIn('template_type', [
                'order_created',
                'order_cancelled',
                'order_completed',
                'order_status_update',
                'order_shipping',
                'payment_reminder',
                'pos_sms',
                'customer_welcome_sms'
            ])->get();

            return response()->json([
                'success' => true,
                'whatsapp_templates' => $templates
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp templates', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve templates'
            ], 500);
        }
    }

    /**
     * Get WhatsApp template list for datatable
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTemplateList(Request $request)
    {
        try {
            $templates = SmsTemplate::whereIn('template_type', [
                'order_created',
                'order_cancelled',
                'order_completed',
                'order_status_update',
                'order_shipping',
                'payment_reminder',
                'pos_sms',
                'customer_welcome_sms'
            ])->orderBy('template_subject', 'asc')->get();

            $totalCount = $templates->count();

            return response()->json([
                'datarows' => $templates,
                'count' => $totalCount
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp template list', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve template list'
            ], 500);
        }
    }

    /**
     * Get specific WhatsApp template
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTemplate($id)
    {
        try {
            $template = SmsTemplate::find($id);

            if (!$template) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template not found'
                ], 404);
            }

            $content = $template->custom_content ?: $template->default_content;

            return response()->json([
                'success' => true,
                'message' => $content,
                'template' => $template
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp template', [
                'template_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve template'
            ], 500);
        }
    }

    /**
     * Get WhatsApp template content for editing
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTemplateContent($id)
    {
        try {
            $template = SmsTemplate::find($id);

            if (!$template) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template not found'
                ], 404);
            }

            $response = [
                'smsSubject' => $template->template_subject,
                'content' => $template->custom_content ?: $template->default_content,
                'isCustom' => !empty($template->custom_content),
            ];

            return response()->json($response);

        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp template content', [
                'template_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve template content'
            ], 500);
        }
    }

    /**
     * Update WhatsApp template content
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setTemplateContent(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'subject' => 'required|string|max:255',
                'custom_content' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $template = SmsTemplate::find($id);

            if (!$template) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template not found'
                ], 404);
            }

            $template->update([
                'template_subject' => $request->subject,
                'custom_content' => $request->custom_content
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Template updated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update WhatsApp template', [
                'template_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update template'
            ], 500);
        }
    }

    /**
     * Send WhatsApp message to customer group
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendWhatsAppMessage(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'custID' => 'required|integer',
                'tempID' => 'required|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get template
            $template = SmsTemplate::find($request->tempID);
            if (!$template) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template not found'
                ], 404);
            }

            // Get customers from customer group
            $customers = Customer::where('customer_group', $request->custID)->get();

            if ($customers->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No customers found in the selected group'
                ], 404);
            }

            $successCount = 0;
            $failureCount = 0;
            $errors = [];

            foreach ($customers as $customer) {
                if (empty($customer->phone_number)) {
                    $failureCount++;
                    continue;
                }

                // Replace template variables
                $message = $this->replaceTemplateVariables(
                    $template->custom_content ?: $template->default_content,
                    $customer
                );

                // Send WhatsApp message
                $result = $this->whatsAppService->sendMessage(
                    $customer->phone_number,
                    $message
                );

                if ($result['success']) {
                    $successCount++;
                } else {
                    $failureCount++;
                    $errors[] = "Failed to send to {$customer->first_name}: " . $result['message'];
                }
            }

            $message = "WhatsApp messages sent successfully to {$successCount} customers.";
            if ($failureCount > 0) {
                $message .= " {$failureCount} messages failed.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'details' => [
                    'success_count' => $successCount,
                    'failure_count' => $failureCount,
                    'errors' => $errors
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp messages', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send WhatsApp messages: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get WhatsApp service status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getServiceStatus()
    {
        try {
            $status = $this->whatsAppService->getStatus();

            return response()->json([
                'success' => true,
                'status' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get WhatsApp service status', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get service status'
            ], 500);
        }
    }

    /**
     * Replace template variables with customer data
     *
     * @param string $template
     * @param Customer $customer
     * @return string
     */
    private function replaceTemplateVariables($template, Customer $customer)
    {
        $variables = [
            '{customer_name}' => $customer->first_name,
            '{customer_full_name}' => $customer->first_name . ' ' . $customer->last_name,
            '{first_name}' => $customer->first_name,
            '{last_name}' => $customer->last_name,
            '{app_name}' => config('app.name', 'Ekaanivita Boutique'),
            '{boutique_name}' => 'Ekaanivita Boutique',
            '{contact_phone}' => '+91 81259 04154',
            '{contact_email}' => '<EMAIL>'
        ];

        return str_replace(array_keys($variables), array_values($variables), $template);
    }
}
