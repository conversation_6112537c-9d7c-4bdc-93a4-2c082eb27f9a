<?php
namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SettingsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Setting::query()->truncate();

        $purchase_code = session('purchase_code');
        DB::table('settings')->insert([
            ['setting_name'     => 'time_format',                   'setting_value'   => '12h'],
            ['setting_name'     => 'date_format',                  'setting_value'   => 'Y-m-d'],
            ['setting_name'     => 'currency_symbol',              'setting_value'   => '$'],
            ['setting_name'     => 'currency_format',              'setting_value'   => 'left'],
            ['setting_name'     => 'thousand_separator',           'setting_value'   => ','],
            ['setting_name'     => 'language_setting',             'setting_value'   => 'english'],
            ['setting_name'     => 'decimal_separator',            'setting_value'   => '.'],
            ['setting_name'     => 'number_of_decimal',            'setting_value'   => '2'],
            ['setting_name'     => 'offday_setting',               'setting_value'   => ''],
            ['setting_name'     => 'email_from_name',              'setting_value'   => ''],
            ['setting_name'     => 'email_from_address',           'setting_value'   => ''],
            ['setting_name'     => 'email_driver',                 'setting_value'   => ''],
            ['setting_name'     => 'email_smtp_host',              'setting_value'   => ''],
            ['setting_name'     => 'email_port',                   'setting_value'   => ''],
            ['setting_name'     => 'email_smtp_password',          'setting_value'   => ''],
            ['setting_name'     => 'email_encryption_type',        'setting_value'   => ''],
            ['setting_name'     => 'mandrill_api',                 'setting_value'   => ''],
            ['setting_name'     => 'sparkpost_api',                'setting_value'   => ''],
            ['setting_name'     => 'mailgun_domain',               'setting_value'   => ''],
            ['setting_name'     => 'mailgun_api',                  'setting_value'   => ''],
            ['setting_name'     => 'max_row_per_table',            'setting_value'   => '10'],
            ['setting_name'     => 'app_name',                     'setting_value'   => 'Gain POS'],
            ['setting_name'     => 'app_logo',                     'setting_value'   => 'default-logo.png'],
            ['setting_name'     => 'currency_code',                'setting_value'   => 'usd'],
            ['setting_name'     => 'purchase_code',                'setting_value'   => $purchase_code],
            ['setting_name'     => 'can_signup',                   'setting_value'   => '1'],
            ['setting_name'     => 'can_login',                    'setting_value'   => '1'],
            ['setting_name'     => 'invoice_prefix',               'setting_value'   => 'POS'],
            ['setting_name'     => 'invoice_suffix',               'setting_value'   => ''],
            ['setting_name'     => 'last_invoice_number',          'setting_value'   => 10000],
            ['setting_name'     => 'auto_generate_invoice',        'setting_value'   => 0],
            ['setting_name'     => 'auto_email_receive',           'setting_value'   => 0],
            ['setting_name'     => 'invoice_starts_from',          'setting_value'   => 10000],
            ['setting_name'     => 'invoiceLogo',                  'setting_value'   => 'default-logo.png'],
            ['setting_name'     => 'purchase_invoice_prefix',      'setting_value'   => 'POS'],
            ['setting_name'     => 'purchase_invoice_suffix',      'setting_value'   => ''],
            ['setting_name'     => 'purchase_last_invoice_number', 'setting_value'   => 10000],
            ['setting_name'     => 'purchase_auto_generate_invoice','setting_value'   => 0],
            ['setting_name'     => 'purchase_invoice_starts_from', 'setting_value'   => 10000],
            ['setting_name'     => 'purchase_invoiceLogo',         'setting_value'   => 'default-logo.png'],
            ['setting_name'     => 're_order',                     'setting_value'   => 10],
            ['setting_name'     => 'sku_prefix',                   'setting_value'   => ''],
            ['setting_name'     => 'sales_return_status',          'setting_value'   => 'sales'],
            ['setting_name'     => 'purchase_return_status',          'setting_value'   => 'purchase'],
            ['setting_name'     => 'offline_mode',                 'setting_value'   => 0],
            ['setting_name'     => 'time_zone',                     'setting_value'   => 'UTC'],
            ['setting_name'     => 'notification_time',             'setting_value'   => '2019-12-25T04:00:00.641Z'],
            ['setting_name'     => 'low_stock_notification',        'setting_value'   => 0],
            ['setting_name'     => 'out_of_stock_products',         'setting_value'   => 0],
            ['setting_name'     => 'sales_list_delete_option',      'setting_value'   => 0],
            ['setting_name'     => 'sms_recive_to_customer',      'setting_value'   => 0],
            ['setting_name'     => 'new_customer_welcome_sms',      'setting_value'   => 0],
            ['setting_name'     => 'sms_from_name_phone_number',    'setting_value'   =>''],
            ['setting_name'     => 'sms_driver',                    'setting_value'   =>''],
            ['setting_name'     => 'key',                           'setting_value'   =>''],
            ['setting_name'     => 'secret_key',                    'setting_value'   =>''],
            ['setting_name'     => 'sales_list_edit_option',      'setting_value'   => 0],
        ]);
    }
}
