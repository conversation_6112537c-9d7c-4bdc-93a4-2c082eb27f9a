<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BoutiqueOption extends Model
{
    use HasFactory;

    protected $table = 'boutique_options';

    protected $fillable = [
        'name',
        'price'
    ];

    protected $casts = [
        'price' => 'decimal:2'
    ];

    // Scope for searching by name
    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where('name', 'LIKE', '%' . $search . '%');
        }
        return $query;
    }

    // Accessor for formatted price
    public function getFormattedPriceAttribute()
    {
        return '₹' . number_format($this->price, 2);
    }
}