<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderBoutique extends Model
{
    protected $table = 'order_boutique';

    protected $fillable = [
        'customer_id',
        'delivery_date',
        'total',
        'advance_paid',
        'total_paid',
        'status',
        'created_by',
        'updated_by'
    ];

    public function items()
    {
        return $this->hasMany(OrderBoutiqueItem::class, 'order_id');
    }

    public function attachments()
    {
        return $this->hasMany(BoutiqueAttachment::class, 'order_id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }
}