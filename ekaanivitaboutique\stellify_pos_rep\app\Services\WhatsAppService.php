<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class WhatsAppService
{
    private $authKey;
    private $baseUrl;
    private $senderId;

    public function __construct()
    {
        $this->authKey = config('services.msg91.auth_key');
        $this->baseUrl = config('services.msg91.base_url', 'https://control.msg91.com/api/v5');
        $this->senderId = config('services.msg91.sender_id');
    }

    /**
     * Send WhatsApp message using MSG91 API
     *
     * @param string $phoneNumber
     * @param string $message
     * @param array $templateData
     * @return array
     */
    public function sendMessage($phoneNumber, $message, $templateData = [])
    {
        try {
            // Clean phone number (remove any non-numeric characters except +)
            $cleanPhone = $this->cleanPhoneNumber($phoneNumber);
            
            if (!$cleanPhone) {
                throw new Exception('Invalid phone number format');
            }

            // Prepare the request payload
            $payload = [
                'integrated_number' => $this->senderId,
                'content_type' => 'text',
                'payload' => [
                    'to' => $cleanPhone,
                    'type' => 'text',
                    'text' => $message
                ]
            ];

            // Send the request to MSG91
            $response = Http::withHeaders([
                'authkey' => $this->authKey,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/whatsapp/whatsapp-outbound-message/', $payload);

            // Log the request and response for debugging
            Log::info('WhatsApp Message Request', [
                'phone' => $cleanPhone,
                'message' => $message,
                'payload' => $payload
            ]);

            Log::info('WhatsApp Message Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    'success' => true,
                    'message' => 'WhatsApp message sent successfully',
                    'data' => $responseData,
                    'phone' => $cleanPhone
                ];
            } else {
                throw new Exception('MSG91 API Error: ' . $response->body());
            }

        } catch (Exception $e) {
            Log::error('WhatsApp Message Failed', [
                'phone' => $phoneNumber,
                'message' => $message,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send WhatsApp message: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send template-based WhatsApp message
     *
     * @param string $phoneNumber
     * @param string $templateName
     * @param array $templateParams
     * @return array
     */
    public function sendTemplateMessage($phoneNumber, $templateName, $templateParams = [])
    {
        try {
            $cleanPhone = $this->cleanPhoneNumber($phoneNumber);
            
            if (!$cleanPhone) {
                throw new Exception('Invalid phone number format');
            }

            $payload = [
                'integrated_number' => $this->senderId,
                'content_type' => 'template',
                'payload' => [
                    'to' => $cleanPhone,
                    'type' => 'template',
                    'template' => [
                        'name' => $templateName,
                        'language' => [
                            'code' => 'en'
                        ],
                        'components' => [
                            [
                                'type' => 'body',
                                'parameters' => $templateParams
                            ]
                        ]
                    ]
                ]
            ];

            $response = Http::withHeaders([
                'authkey' => $this->authKey,
                'Content-Type' => 'application/json'
            ])->post($this->baseUrl . '/whatsapp/whatsapp-outbound-message/', $payload);

            Log::info('WhatsApp Template Message Request', [
                'phone' => $cleanPhone,
                'template' => $templateName,
                'params' => $templateParams
            ]);

            Log::info('WhatsApp Template Message Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            if ($response->successful()) {
                $responseData = $response->json();
                return [
                    'success' => true,
                    'message' => 'WhatsApp template message sent successfully',
                    'data' => $responseData,
                    'phone' => $cleanPhone
                ];
            } else {
                throw new Exception('MSG91 API Error: ' . $response->body());
            }

        } catch (Exception $e) {
            Log::error('WhatsApp Template Message Failed', [
                'phone' => $phoneNumber,
                'template' => $templateName,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send WhatsApp template message: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Clean and format phone number
     *
     * @param string $phoneNumber
     * @return string|null
     */
    private function cleanPhoneNumber($phoneNumber)
    {
        // Remove all non-numeric characters except +
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);
        
        // If number starts with +91, keep it as is
        if (strpos($cleaned, '+91') === 0) {
            return $cleaned;
        }
        
        // If number starts with 91, add +
        if (strpos($cleaned, '91') === 0 && strlen($cleaned) === 12) {
            return '+' . $cleaned;
        }
        
        // If number is 10 digits, add +91
        if (strlen($cleaned) === 10 && is_numeric($cleaned)) {
            return '+91' . $cleaned;
        }
        
        // If number starts with 0, remove 0 and add +91
        if (strpos($cleaned, '0') === 0 && strlen($cleaned) === 11) {
            return '+91' . substr($cleaned, 1);
        }
        
        return null;
    }

    /**
     * Check if WhatsApp service is configured
     *
     * @return bool
     */
    public function isConfigured()
    {
        return !empty($this->authKey) && !empty($this->senderId);
    }

    /**
     * Get service status
     *
     * @return array
     */
    public function getStatus()
    {
        return [
            'configured' => $this->isConfigured(),
            'auth_key_set' => !empty($this->authKey),
            'sender_id_set' => !empty($this->senderId),
            'base_url' => $this->baseUrl
        ];
    }
}
