<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WhatsAppService;
use App\Services\OrderNotificationService;
use App\Models\BoutiqueOrders;
use App\Models\Customer;

class TestWhatsAppService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:test {--phone=} {--order-id=} {--type=status}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test WhatsApp service integration';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Testing WhatsApp Service Integration...');
        $this->line('');

        // Test service configuration
        $this->testServiceConfiguration();

        // Test message sending if phone number provided
        if ($this->option('phone')) {
            $this->testMessageSending();
        }

        // Test order notification if order ID provided
        if ($this->option('order-id')) {
            $this->testOrderNotification();
        }

        $this->line('');
        $this->info('WhatsApp service test completed!');

        return 0;
    }

    /**
     * Test service configuration
     */
    private function testServiceConfiguration()
    {
        $this->info('1. Testing Service Configuration...');

        $whatsAppService = new WhatsAppService();
        $status = $whatsAppService->getStatus();

        $this->table(
            ['Configuration', 'Status'],
            [
                ['Service Configured', $status['configured'] ? '✅ Yes' : '❌ No'],
                ['Auth Key Set', $status['auth_key_set'] ? '✅ Yes' : '❌ No'],
                ['Sender ID Set', $status['sender_id_set'] ? '✅ Yes' : '❌ No'],
                ['Base URL', $status['base_url']],
            ]
        );

        if (!$status['configured']) {
            $this->error('⚠️  WhatsApp service is not properly configured!');
            $this->line('Please check your .env file for MSG91_AUTH_KEY and MSG91_SENDER_ID');
        } else {
            $this->info('✅ WhatsApp service is properly configured');
        }

        $this->line('');
    }

    /**
     * Test message sending
     */
    private function testMessageSending()
    {
        $this->info('2. Testing Message Sending...');

        $phone = $this->option('phone');
        $whatsAppService = new WhatsAppService();

        $testMessage = "Hello! This is a test message from Ekaanivita Boutique WhatsApp integration.\n\n" .
                      "If you receive this message, the integration is working correctly.\n\n" .
                      "Time: " . now()->format('Y-m-d H:i:s');

        $this->line("Sending test message to: {$phone}");

        $result = $whatsAppService->sendMessage($phone, $testMessage);

        if ($result['success']) {
            $this->info('✅ Test message sent successfully!');
            $this->line("Response: " . json_encode($result['data'], JSON_PRETTY_PRINT));
        } else {
            $this->error('❌ Failed to send test message');
            $this->line("Error: " . $result['message']);
        }

        $this->line('');
    }

    /**
     * Test order notification
     */
    private function testOrderNotification()
    {
        $this->info('3. Testing Order Notification...');

        $orderId = $this->option('order-id');
        $type = $this->option('type');

        $order = BoutiqueOrders::with('customer')->find($orderId);

        if (!$order) {
            $this->error("❌ Order with ID {$orderId} not found");
            return;
        }

        if (!$order->customer) {
            $this->error("❌ Customer not found for order {$orderId}");
            return;
        }

        if (!$order->customer->phone_number) {
            $this->error("❌ Customer phone number not found for order {$orderId}");
            return;
        }

        $this->line("Order ID: {$order->id}");
        $this->line("Customer: {$order->customer->first_name} {$order->customer->last_name}");
        $this->line("Phone: {$order->customer->phone_number}");
        $this->line("Notification Type: {$type}");

        $whatsAppService = new WhatsAppService();
        $notificationService = new OrderNotificationService($whatsAppService);

        switch ($type) {
            case 'created':
                $result = $notificationService->sendOrderCreatedNotification($order);
                break;
            case 'cancelled':
                $result = $notificationService->sendOrderCancelledNotification($order);
                break;
            case 'completed':
                $result = $notificationService->sendOrderCompletedNotification($order);
                break;
            case 'status':
            default:
                $result = $notificationService->sendOrderStatusUpdateNotification($order, 'Pending', $order->status);
                break;
        }

        if ($result['success']) {
            $this->info('✅ Order notification sent successfully!');
        } else {
            $this->error('❌ Failed to send order notification');
            $this->line("Error: " . $result['message']);
        }

        $this->line('');
    }
}
