<?php

namespace App\Exports;

use App\Models\Customer;
use App\Models\CustomerGroup;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class CustomersExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        return Customer::query();
    }

    public function map($customer): array
    {
        if ($customer->customer_group) {
            $customer_group = CustomerGroup::where('id', $customer->customer_group)->pluck('title')->first();
        } else {
            $customer_group = '';
        }

        

        return [
            $customer->first_name. ''.$customer->last_name,
            $customer->company ?? null,
            $customer->email ?? null,
            $customer->phone_number ?? null,
            $customer_group ?? null
            
        ];

    }

    public function headings(): array
    {
        return [
            
            'Name',
            'Company',
            'Email',
            'Phone Number',
            'Customer Group'
            
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:K1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );
            },
        ];
    }
}
