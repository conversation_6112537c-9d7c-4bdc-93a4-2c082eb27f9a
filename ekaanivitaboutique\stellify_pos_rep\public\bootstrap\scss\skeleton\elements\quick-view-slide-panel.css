.body-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.36);
  opacity: 0.4;
  z-index: 9999;
  display: none; }

body.quick-panel-open {
  overflow: hidden; }
  body.quick-panel-open .body-overlay {
    display: block; }
  body.quick-panel-open .quick-view-panel {
    visibility: visible; }
    body.quick-panel-open .quick-view-panel .quick-avatar-cover {
      height: 150px;
      background: #4a97fd; }
    body.quick-panel-open .quick-view-panel .avatar-card {
      height: 150px;
      width: 150px;
      margin: -100px auto 0; }
  body.quick-panel-open .close {
    font-size: 1.8rem;
    padding: 0.9rem !important;
    color: #ffffff;
    cursor: pointer;
    float: right;
    line-height: 20px;
    position: absolute;
    right: 0rem;
    top: 0rem;
    font-weight: normal;
    outline: none; }
    body.quick-panel-open .close:focus {
      outline: none; }

.quick-view-panel {
  overflow: scroll;
  background: #ffffff;
  padding: 1.5rem;
  position: fixed;
  width: 480px;
  max-width: 32rem;
  height: 100vh;
  top: 0;
  right: -32rem;
  z-index: 10000;
  visibility: hidden;
  -webkit-transition: right 400ms cubic-bezier(0.05, 0.74, 0.27, 0.99) 0s;
  -moz-transition: right 400ms cubic-bezier(0.05, 0.74, 0.27, 0.99) 0s;
  -ms-transition: right 400ms cubic-bezier(0.05, 0.74, 0.27, 0.99) 0s;
  -o-transition: right 400ms cubic-bezier(0.05, 0.74, 0.27, 0.99) 0s;
  transition: right 400ms cubic-bezier(0.05, 0.74, 0.27, 0.99) 0s; }

@media (max-width: 480px) {
  .quick-view-panel {
    width: 100%;
    max-width: 100%; } }

/*# sourceMappingURL=quick-view-slide-panel.css.map */
