<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class InsertWhatsappTemplates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Insert WhatsApp templates for order notifications
        $templates = [
            [
                'template_type' => 'order_created',
                'template_subject' => 'Order Created Notification',
                'default_content' => "Dear {customer_name},\n\nYour order #{order_id} has been successfully created at {boutique_name}.\n\nOrder Details:\n- Delivery Date: {delivery_date}\n- Total Amount: ₹{total_amount}\n- Advance Paid: ₹{advance_paid}\n- Balance: ₹{balance_amount}\n\nThank you for choosing {boutique_name}!\n\nContact: {contact_phone}\nEmail: {contact_email}",
                'custom_content' => '',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'template_type' => 'order_cancelled',
                'template_subject' => 'Order Cancelled Notification',
                'default_content' => "Dear {customer_name},\n\nWe regret to inform you that your order #{order_id} has been cancelled.\n\nReason: {cancellation_reason}\n\nIf you have any questions, please contact us.\n\nContact: {contact_phone}\nEmail: {contact_email}\n\nThank you for your understanding.\n{boutique_name}",
                'custom_content' => '',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'template_type' => 'order_completed',
                'template_subject' => 'Order Completed Notification',
                'default_content' => "Dear {customer_name},\n\nGreat news! Your order #{order_id} has been completed and is ready for delivery.\n\nOrder Details:\n- Total Amount: ₹{total_amount}\n- Amount Paid: ₹{advance_paid}\n- Balance Due: ₹{balance_amount}\n\nThank you for choosing {boutique_name}!\n\nContact: {contact_phone}\nEmail: {contact_email}",
                'custom_content' => '',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'template_type' => 'order_status_update',
                'template_subject' => 'Order Status Update Notification',
                'default_content' => "Dear {customer_name},\n\nYour order #{order_id} status has been updated to: {order_status}\n\nDelivery Date: {delivery_date}\n\nThank you for choosing {boutique_name}!\n\nContact: {contact_phone}\nEmail: {contact_email}",
                'custom_content' => '',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'template_type' => 'order_shipping',
                'template_subject' => 'Order Shipping Notification',
                'default_content' => "Dear {customer_name},\n\nYour order #{order_id} is now being shipped!\n\nExpected Delivery Date: {delivery_date}\n\nWe will notify you once your order is delivered.\n\nThank you for choosing {boutique_name}!\n\nContact: {contact_phone}\nEmail: {contact_email}",
                'custom_content' => '',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'template_type' => 'payment_reminder',
                'template_subject' => 'Payment Reminder Notification',
                'default_content' => "Dear {customer_name},\n\nThis is a friendly reminder about the pending balance for your order #{order_id}.\n\nOrder Details:\n- Total Amount: ₹{total_amount}\n- Amount Paid: ₹{advance_paid}\n- Balance Due: ₹{balance_amount}\n- Delivery Date: {delivery_date}\n\nPlease complete the payment before delivery.\n\nContact: {contact_phone}\nEmail: {contact_email}\n\nThank you!\n{boutique_name}",
                'custom_content' => '',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // Insert templates using MySQL-compatible approach
        foreach ($templates as $template) {
            // Check if template already exists
            $exists = DB::table('sms_templates')
                ->where('template_type', $template['template_type'])
                ->exists();

            if (!$exists) {
                DB::table('sms_templates')->insert($template);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove the WhatsApp templates
        $templateTypes = [
            'order_created',
            'order_cancelled',
            'order_completed',
            'order_status_update',
            'order_shipping',
            'payment_reminder'
        ];

        DB::table('sms_templates')
            ->whereIn('template_type', $templateTypes)
            ->delete();
    }
}
