const { DataTypes } = require('sequelize');

/**
 * BoutiqueOption model factory for boutique_options table
 * @param {Sequelize} sequelize - Sequelize instance
 */
const BoutiqueOptionModel = (sequelize) => sequelize.define('BoutiqueOption', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false,
    field: 'id',
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'name',
    validate: {
      notEmpty: {
        msg: 'Name cannot be empty'
      },
      len: {
        args: [1, 255],
        msg: 'Name must be between 1 and 255 characters'
      }
    }
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    field: 'price',
    validate: {
      isDecimal: {
        msg: 'Price must be a valid decimal number'
      },
      min: {
        args: [0],
        msg: 'Price cannot be negative'
      }
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'created_at',
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'updated_at',
  },
}, {
  tableName: 'boutique_options',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['name']
    }
  ],
  hooks: {
    beforeValidate: (option) => {
      if (option.name) {
        option.name = option.name.trim();
      }
    }
  }
});

// Instance methods
BoutiqueOptionModel.prototype.getFormattedPrice = function() {
  return `₹${parseFloat(this.price).toFixed(2)}`;
};

// Class methods
BoutiqueOptionModel.findByName = function(name) {
  return this.findOne({
    where: {
      name: {
        [require('sequelize').Op.iLike]: `%${name}%`
      }
    }
  });
};

BoutiqueOptionModel.searchByName = function(searchTerm, limit = 10, offset = 0) {
  return this.findAndCountAll({
    where: {
      name: {
        [require('sequelize').Op.iLike]: `%${searchTerm}%`
      }
    },
    limit,
    offset,
    order: [['name', 'ASC']]
  });
};

module.exports = { BoutiqueOptionModel };
