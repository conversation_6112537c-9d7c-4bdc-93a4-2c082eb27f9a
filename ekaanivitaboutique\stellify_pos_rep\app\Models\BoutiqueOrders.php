<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BoutiqueOrders extends Model
{
    use HasFactory;

    protected $table = 'order_boutique';

    protected $fillable = [
        'customer_id',
        'delivery_date',
        'status',
        'subtotal_amount',
        'discount_percent',
        'discount_amount',
        'total',
        'advance_paid',
        'payment_mode',
        'payment_reference',
        'payment_status',
        'balance_amount',
        'prices_modified',
        'created_by',
        'updated_by'
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function attachments()
    {
        return $this->hasMany(BoutiqueAttachment::class, 'order_id');
    }

    public function items()
    {
        return $this->hasMany(OrderBoutiqueItem::class, 'order_id');
    }
}