@font-face {
  font-family: 'fontello';
  src: url('../font/fontello.eot?65892414');
  src: url('../font/fontello.eot?65892414#iefix') format('embedded-opentype'),
       url('../font/fontello.woff2?65892414') format('woff2'),
       url('../font/fontello.woff?65892414') format('woff'),
       url('../font/fontello.ttf?65892414') format('truetype'),
       url('../font/fontello.svg?65892414#fontello') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: url('../font/fontello.svg?65892414#fontello') format('svg');
  }
}
*/
 
 [class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  speak: none;
 
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */
 
  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;
 
  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;
 
  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;
 
  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */
 
  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
 
  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}
 
.icon-heart-empty:before { content: '\e800'; } /* '' */
.icon-power-off:before { content: '\e801'; } /* '' */
.icon-calendar:before { content: '\e802'; } /* '' */
.icon-heart:before { content: '\e803'; } /* '' */
.icon-location:before { content: '\e804'; } /* '' */
.icon-cog:before { content: '\e805'; } /* '' */
.icon-bell:before { content: '\e806'; } /* '' */
.icon-mail:before { content: '\e807'; } /* '' */
.icon-angle-left:before { content: '\e808'; } /* '' */
.icon-angle-right:before { content: '\e809'; } /* '' */
.icon-angle-down:before { content: '\e80a'; } /* '' */
.icon-angle-up:before { content: '\e80b'; } /* '' */
.icon-users:before { content: '\e80c'; } /* '' */
.icon-user:before { content: '\e80d'; } /* '' */
.icon-user-add:before { content: '\e80e'; } /* '' */
.icon-search:before { content: '\e80f'; } /* '' */
.icon-plus-squared:before { content: '\e810'; } /* '' */
.icon-minus:before { content: '\e811'; } /* '' */
.icon-plus:before { content: '\e812'; } /* '' */
.icon-cancel-circled:before { content: '\e813'; } /* '' */
.icon-cancel-squared:before { content: '\e814'; } /* '' */
.icon-plus-circled:before { content: '\e815'; } /* '' */
.icon-minus-circled:before { content: '\e816'; } /* '' */
.icon-minus-squared:before { content: '\e817'; } /* '' */
.icon-help:before { content: '\e818'; } /* '' */
.icon-help-circled:before { content: '\e819'; } /* '' */
.icon-info-circled:before { content: '\e81a'; } /* '' */
.icon-remove-roommate:before { content: '\e81b'; } /* '' */
.icon-thumbs-up:before { content: '\e81c'; } /* '' */
.icon-thumbs-down:before { content: '\e81d'; } /* '' */
.icon-download:before { content: '\e81e'; } /* '' */
.icon-upload:before { content: '\e81f'; } /* '' */
.icon-upload-cloud:before { content: '\e820'; } /* '' */
.icon-export:before { content: '\e821'; } /* '' */
.icon-retweet:before { content: '\e822'; } /* '' */
.icon-chat:before { content: '\e823'; } /* '' */
.icon-alert-roommate:before { content: '\e824'; } /* '' */
.icon-attention:before { content: '\e825'; } /* '' */
.icon-share:before { content: '\e826'; } /* '' */
.icon-ccw:before { content: '\e827'; } /* '' */
.icon-cw:before { content: '\e828'; } /* '' */
.icon-arrows-ccw:before { content: '\e829'; } /* '' */
.icon-dot-3:before { content: '\e82a'; } /* '' */
.icon-pencil:before { content: '\e82b'; } /* '' */
.icon-edit:before { content: '\e82c'; } /* '' */
.icon-attach:before { content: '\e82d'; } /* '' */
.icon-group:before { content: '\e82e'; } /* '' */
.icon-user-1:before { content: '\e82f'; } /* '' */
.icon-paper-plane:before { content: '\e830'; } /* '' */
.icon-chart-bar:before { content: '\e831'; } /* '' */
.icon-doc:before { content: '\e832'; } /* '' */
.icon-off-1:before { content: '\e833'; } /* '' */
.icon-ok-circled:before { content: '\e834'; } /* '' */
.icon-ok:before { content: '\e835'; } /* '' */
.icon-attention-circled:before { content: '\e836'; } /* '' */
.icon-conversation:before { content: '\e837'; } /* '' */
.icon-user-attention-1:before { content: '\e838'; } /* '' */
.icon-briefcase:before { content: '\e839'; } /* '' */
.icon-lock:before { content: '\e83a'; } /* '' */
.icon-tag:before { content: '\e83b'; } /* '' */
.icon-add-roommate:before { content: '\e83c'; } /* '' */
.icon-phone:before { content: '\e83d'; } /* '' */
.icon-g-home:before { content: '\e83e'; } /* '' */
.icon-g-heart:before { content: '\e83f'; } /* '' */
.icon-barcode:before { content: '\e840'; } /* '' */
.icon-basket:before { content: '\e841'; } /* '' */
.icon-clock:before { content: '\e842'; } /* '' */
.icon-address-book:before { content: '\e843'; } /* '' */
.icon-home:before { content: '\e844'; } /* '' */
.icon-cancel:before { content: '\e845'; } /* '' */
.icon-th:before { content: '\e846'; } /* '' */
.icon-th-large:before { content: '\e847'; } /* '' */
.icon-star-empty:before { content: '\e848'; } /* '' */
.icon-star:before { content: '\e849'; } /* '' */
.icon-down:before { content: '\e84a'; } /* '' */
.icon-up:before { content: '\e84b'; } /* '' */
.icon-g-close:before { content: '\e84c'; } /* '' */
.icon-g-calendar:before { content: '\e84d'; } /* '' */
.icon-g-shopping-cart:before { content: '\e84e'; } /* '' */
.icon-g-time:before { content: '\e84f'; } /* '' */
.icon-g-link-ext:before { content: '\e850'; } /* '' */
.icon-g-check:before { content: '\e851'; } /* '' */
.icon-g-eye:before { content: '\e852'; } /* '' */
.icon-g-email:before { content: '\e853'; } /* '' */
.icon-g-star:before { content: '\e854'; } /* '' */
.icon-g-user:before { content: '\e855'; } /* '' */
.icon-g-search:before { content: '\e856'; } /* '' */
.icon-g-arrow-down:before { content: '\e857'; } /* '' */
.icon-g-arrow-left:before { content: '\e858'; } /* '' */
.icon-g-arrow-right:before { content: '\e859'; } /* '' */
.icon-g-arrow-up:before { content: '\e85a'; } /* '' */
.icon-g-facebook:before { content: '\e85b'; } /* '' */
.icon-g-twitter-alt:before { content: '\e85c'; } /* '' */
.icon-g-angle-down:before { content: '\e85d'; } /* '' */
.icon-g-angle-left:before { content: '\e85e'; } /* '' */
.icon-g-angle-right:before { content: '\e85f'; } /* '' */
.icon-g-angle-up:before { content: '\e860'; } /* '' */
.icon-g-th-large:before { content: '\e861'; } /* '' */
.icon-g-menu:before { content: '\e862'; } /* '' */
.icon-g-money:before { content: '\e863'; } /* '' */
.icon-g-more-alt:before { content: '\e864'; } /* '' */
.icon-g-alert:before { content: '\e865'; } /* '' */
.icon-g-face-smile:before { content: '\e866'; } /* '' */
.icon-g-na:before { content: '\e867'; } /* '' */
.icon-g-arrow-circle-left:before { content: '\e868'; } /* '' */
.icon-g-arrow-circle-right:before { content: '\e869'; } /* '' */
.icon-g-align-left:before { content: '\e86a'; } /* '' */
.icon-g-align-right:before { content: '\e86b'; } /* '' */
.icon-trash:before { content: '\e86c'; } /* '' */
.icon-contact:before { content: '\e86d'; } /* '' */
.icon-bar-chart-alt:before { content: '\e86e'; } /* '' */
.icon-g-bell:before { content: '\e86f'; } /* '' */
.icon-menu:before { content: '\f008'; } /* '' */
.icon-gplus:before { content: '\f05a'; } /* '' */
.icon-link-ext:before { content: '\f08e'; } /* '' */
.icon-twitter:before { content: '\f099'; } /* '' */
.icon-facebook:before { content: '\f09a'; } /* '' */
.icon-tasks:before { content: '\f0ae'; } /* '' */
.icon-filter:before { content: '\f0b0'; } /* '' */
.icon-docs:before { content: '\f0c5'; } /* '' */
.icon-table:before { content: '\f0ce'; } /* '' */
.icon-money:before { content: '\f0d6'; } /* '' */
.icon-columns:before { content: '\f0db'; } /* '' */
.icon-mail-alt:before { content: '\f0e0'; } /* '' */
.icon-linkedin:before { content: '\f0e1'; } /* '' */
.icon-bell-alt:before { content: '\f0f3'; } /* '' */
.icon-doc-text:before { content: '\f0f6'; } /* '' */
.icon-building:before { content: '\f0f7'; } /* '' */
.icon-circle-empty:before { content: '\f10c'; } /* '' */
.icon-circle:before { content: '\f111'; } /* '' */
.icon-smile:before { content: '\f118'; } /* '' */
.icon-attention-alt:before { content: '\f12a'; } /* '' */
.icon-bullseye:before { content: '\f140'; } /* '' */
.icon-ellipsis-vert:before { content: '\f142'; } /* '' */
.icon-doc-inv:before { content: '\f15b'; } /* '' */
.icon-doc-text-inv:before { content: '\f15c'; } /* '' */
.icon-instagram:before { content: '\f16d'; } /* '' */
.icon-box:before { content: '\f187'; } /* '' */
.icon-building-filled:before { content: '\f1ad'; } /* '' */
.icon-file-pdf:before { content: '\f1c1'; } /* '' */
.icon-file-word:before { content: '\f1c2'; } /* '' */
.icon-file-excel:before { content: '\f1c3'; } /* '' */
.icon-file-powerpoint:before { content: '\f1c4'; } /* '' */
.icon-file-image:before { content: '\f1c5'; } /* '' */
.icon-file-archive:before { content: '\f1c6'; } /* '' */
.icon-file-audio:before { content: '\f1c7'; } /* '' */
.icon-file-video:before { content: '\f1c8'; } /* '' */
.icon-file-code:before { content: '\f1c9'; } /* '' */
.icon-circle-thin:before { content: '\f1db'; } /* '' */
.icon-bell-off:before { content: '\f1f6'; } /* '' */
.icon-bell-off-empty:before { content: '\f1f7'; } /* '' */
.icon-birthday:before { content: '\f1fd'; } /* '' */
.icon-venus:before { content: '\f221'; } /* '' */
.icon-mars:before { content: '\f222'; } /* '' */
.icon-venus-mars:before { content: '\f228'; } /* '' */
.icon-bed:before { content: '\f236'; } /* '' */
.icon-hourglass-2:before { content: '\f252'; } /* '' */
.icon-commenting:before { content: '\f27a'; } /* '' */